const Users = require('../models/userModel')
const Posts = require('../models/postModel')

const adminCtrl = {
    // Get all users for admin dashboard
    getAllUsers: async (req, res) => {
        try {
            const users = await Users.find({})
                .select('-password')
                .sort('-createdAt')

            res.json({users})
        } catch (err) {
            return res.status(500).json({msg: err.message})
        }
    },

    // Get user stats (followers, following, posts, likes)
    getUserStats: async (req, res) => {
        try {
            const user = await Users.findById(req.params.id)
                .select('-password')
                .populate("followers following", "avatar username fullname")

            if(!user) return res.status(400).json({msg: "User does not exist."})

            // Get user posts count
            const postsCount = await Posts.find({user: user._id}).count()

            // Get user posts with likes count
            const posts = await Posts.find({user: user._id})
                .populate("user likes", "avatar username fullname")
                .sort('-createdAt')
                .limit(5)

            // Calculate total likes received
            let totalLikes = 0
            posts.forEach(post => {
                totalLikes += post.likes.length
            })

            res.json({
                user,
                postsCount,
                posts,
                totalLikes
            })
        } catch (err) {
            return res.status(500).json({msg: err.message})
        }
    },

    // Remove a user (admin only)
    removeUser: async (req, res) => {
        try {
            const user = await Users.findById(req.params.id)

            if(!user) return res.status(400).json({msg: "User does not exist."})

            // Remove user's posts
            await Posts.deleteMany({user: req.params.id})

            // Remove user from followers/following lists
            await Users.updateMany({}, {
                $pull: { followers: req.params.id, following: req.params.id }
            })

            // Remove user's comments
            const Comments = require('../models/commentModel')
            await Comments.deleteMany({user: req.params.id})

            // Remove user's notifications
            const Notifies = require('../models/notifyModel')
            await Notifies.deleteMany({user: req.params.id})

            // Update notifications that reference this user
            await Notifies.updateMany(
                {"user": {$exists: false}},
                {$set: {"user": null}}
            )

            // Remove user's messages
            const Messages = require('../models/messageModel')
            await Messages.deleteMany({sender: req.params.id})
            await Messages.deleteMany({recipient: req.params.id})

            // Finally delete the user
            await Users.findByIdAndDelete(req.params.id)

            res.json({msg: "User deleted successfully!"})
        } catch (err) {
            return res.status(500).json({msg: err.message})
        }
    }
}

module.exports = adminCtrl
