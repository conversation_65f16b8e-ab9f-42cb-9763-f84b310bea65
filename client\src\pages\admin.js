import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useHistory } from 'react-router-dom'
import { GLOBALTYPES } from '../redux/actions/globalTypes'
import { getDataAPI, deleteDataAP<PERSON> } from '../utils/fetchData'

const Admin = () => {
    const { auth } = useSelector(state => state)
    const dispatch = useDispatch()
    const history = useHistory()

    const [users, setUsers] = useState([])
    const [loading, setLoading] = useState(false)
    const [selectedUser, setSelectedUser] = useState(null)
    const [userStats, setUserStats] = useState(null)
    const [callback, setCallback] = useState(false)
    const [activeTab, setActiveTab] = useState('users')

    useEffect(() => {
        if(auth.user && auth.user.role !== 'admin') {
            dispatch({
                type: GLOBALTYPES.ALERT,
                payload: {error: "Access denied. Admin privileges required."}
            })
            return history.push('/')
        }

        const fetchUsers = async () => {
            setLoading(true)
            try {
                const res = await getDataAPI('admin/users', auth.token)
                setUsers(res.data.users)
            } catch (err) {
                dispatch({
                    type: GLOBALTYPES.ALERT,
                    payload: {error: err.response.data.msg}
                })
            }
            setLoading(false)
        }

        fetchUsers()
    }, [auth.token, auth.user, dispatch, history, callback])

    const handleViewUser = async (userId) => {
        try {
            const res = await getDataAPI(`admin/user/${userId}`, auth.token)
            setSelectedUser(res.data.user)
            setUserStats({
                postsCount: res.data.postsCount,
                followersCount: res.data.user.followers.length,
                followingCount: res.data.user.following.length,
                totalLikes: res.data.totalLikes,
                posts: res.data.posts
            })
        } catch (err) {
            dispatch({
                type: GLOBALTYPES.ALERT,
                payload: {error: err.response.data.msg}
            })
        }
    }

    const handleRemoveUser = async (userId) => {
        if(window.confirm("Are you sure you want to remove this user? This action cannot be undone.")) {
            try {
                await deleteDataAPI(`admin/user/${userId}`, auth.token)
                dispatch({
                    type: GLOBALTYPES.ALERT,
                    payload: {success: "User removed successfully."}
                })
                setCallback(!callback)
                setSelectedUser(null)
                setUserStats(null)
            } catch (err) {
                dispatch({
                    type: GLOBALTYPES.ALERT,
                    payload: {error: err.response.data.msg}
                })
            }
        }
    }

    return (
        <div className="admin_page">
            <div className="container">
                <h2 className="text-center my-4">Admin Dashboard</h2>
                
                {/* Navigation Tabs */}
                <ul className="nav nav-tabs mb-4">
                    <li className="nav-item">
                        <a 
                            className={`nav-link ${activeTab === 'users' ? 'active' : ''}`} 
                            href="#"
                            onClick={(e) => {
                                e.preventDefault();
                                setActiveTab('users');
                            }}
                        >
                            <i className="fas fa-users mr-2"></i>
                            User Management
                        </a>
                    </li>
                    <li className="nav-item">
                        <a 
                            className={`nav-link ${activeTab === 'stats' ? 'active' : ''}`} 
                            href="#"
                            onClick={(e) => {
                                e.preventDefault();
                                setActiveTab('stats');
                            }}
                        >
                            <i className="fas fa-chart-bar mr-2"></i>
                            Site Statistics
                        </a>
                    </li>
                </ul>
                
                {/* User Management Tab */}
                {activeTab === 'users' && (
                    <div className="row">
                        {/* Users List */}
                        <div className="col-md-6">
                            <div className="card">
                                <div className="card-header">
                                    <h5>Users</h5>
                                </div>
                                <div className="card-body" style={{maxHeight: '500px', overflow: 'auto'}}>
                                    {loading ? (
                                        <div className="text-center">Loading...</div>
                                    ) : (
                                        <table className="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Username</th>
                                                    <th>Role</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {users.map(user => (
                                                    <tr key={user._id} className={selectedUser && selectedUser._id === user._id ? 'table-primary' : ''}>
                                                        <td>{user.username}</td>
                                                        <td>{user.role}</td>
                                                        <td>
                                                            <button 
                                                                className="btn btn-info btn-sm mr-2"
                                                                onClick={() => handleViewUser(user._id)}
                                                            >
                                                                View
                                                            </button>
                                                            {user.role !== 'admin' && (
                                                                <button 
                                                                    className="btn btn-danger btn-sm"
                                                                    onClick={() => handleRemoveUser(user._id)}
                                                                >
                                                                    Remove
                                                                </button>
                                                            )}
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    )}
                                </div>
                            </div>
                        </div>
                        
                        {/* User Details */}
                        <div className="col-md-6">
                            <div className="card">
                                <div className="card-header">
                                    <h5>User Details</h5>
                                </div>
                                <div className="card-body">
                                    {selectedUser ? (
                                        <div>
                                            <div className="d-flex align-items-center mb-3">
                                                <img 
                                                    src={selectedUser.avatar} 
                                                    alt={selectedUser.username} 
                                                    className="rounded-circle mr-2"
                                                    style={{width: '50px', height: '50px'}}
                                                />
                                                <div>
                                                    <h5>{selectedUser.fullname}</h5>
                                                    <p className="text-muted mb-0">@{selectedUser.username}</p>
                                                </div>
                                            </div>
                                            
                                            <div className="row mb-3">
                                                <div className="col-3 text-center">
                                                    <h6>{userStats?.postsCount || 0}</h6>
                                                    <small className="text-muted">Posts</small>
                                                </div>
                                                <div className="col-3 text-center">
                                                    <h6>{userStats?.followersCount || 0}</h6>
                                                    <small className="text-muted">Followers</small>
                                                </div>
                                                <div className="col-3 text-center">
                                                    <h6>{userStats?.followingCount || 0}</h6>
                                                    <small className="text-muted">Following</small>
                                                </div>
                                                <div className="col-3 text-center">
                                                    <h6>{userStats?.totalLikes || 0}</h6>
                                                    <small className="text-muted">Likes</small>
                                                </div>
                                            </div>
                                            
                                            <div className="mb-3">
                                                <p><strong>Email:</strong> {selectedUser.email}</p>
                                                <p><strong>Gender:</strong> {selectedUser.gender}</p>
                                                <p><strong>Role:</strong> {selectedUser.role}</p>
                                                <p><strong>Joined:</strong> {new Date(selectedUser.createdAt).toLocaleDateString()}</p>
                                            </div>
                                            
                                            {/* Recent Posts */}
                                            {userStats?.posts && userStats.posts.length > 0 && (
                                                <div className="mb-3">
                                                    <h6 className="mb-2">Recent Posts</h6>
                                                    <div style={{maxHeight: '200px', overflow: 'auto'}}>
                                                        {userStats.posts.map(post => (
                                                            <div key={post._id} className="card mb-2">
                                                                <div className="card-body p-2">
                                                                    <div className="d-flex justify-content-between align-items-center">
                                                                        <p className="mb-0 text-truncate" style={{maxWidth: '200px'}}>
                                                                            {post.content || 'Image post'}
                                                                        </p>
                                                                        <div>
                                                                            <span className="badge badge-primary mr-1">
                                                                                <i className="far fa-thumbs-up"></i> {post.likes.length}
                                                                            </span>
                                                                            <span className="badge badge-info">
                                                                                <i className="far fa-comment"></i> {post.comments.length}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                            
                                            {selectedUser.role !== 'admin' && (
                                                <button 
                                                    className="btn btn-danger btn-block"
                                                    onClick={() => handleRemoveUser(selectedUser._id)}
                                                >
                                                    Remove User
                                                </button>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="text-center py-5">
                                            <p className="text-muted">Select a user to view details</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Site Statistics Tab */}
                {activeTab === 'stats' && (
                    <div className="row">
                        <div className="col-md-12">
                            <div className="card">
                                <div className="card-header">
                                    <h5>Site Overview</h5>
                                </div>
                                <div className="card-body">
                                    <div className="row">
                                        <div className="col-md-3">
                                            <div className="card bg-primary text-white mb-3">
                                                <div className="card-body text-center">
                                                    <h3>{users.length}</h3>
                                                    <h6>Total Users</h6>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-md-3">
                                            <div className="card bg-success text-white mb-3">
                                                <div className="card-body text-center">
                                                    <h3>{users.filter(user => user.role === 'admin').length}</h3>
                                                    <h6>Admins</h6>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-md-3">
                                            <div className="card bg-info text-white mb-3">
                                                <div className="card-body text-center">
                                                    <h3>{users.filter(user => user.role === 'user').length}</h3>
                                                    <h6>Regular Users</h6>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-md-3">
                                            <div className="card bg-warning text-white mb-3">
                                                <div className="card-body text-center">
                                                    <h3>{new Date().toLocaleDateString()}</h3>
                                                    <h6>Current Date</h6>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="mt-4">
                                        <h5>Recent Users</h5>
                                        <table className="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Username</th>
                                                    <th>Full Name</th>
                                                    <th>Email</th>
                                                    <th>Role</th>
                                                    <th>Joined</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {users.slice(0, 5).map(user => (
                                                    <tr key={user._id}>
                                                        <td>{user.username}</td>
                                                        <td>{user.fullname}</td>
                                                        <td>{user.email}</td>
                                                        <td>{user.role}</td>
                                                        <td>{new Date(user.createdAt).toLocaleDateString()}</td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}

export default Admin
