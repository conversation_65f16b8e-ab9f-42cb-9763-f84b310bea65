import React from 'react'
import <PERSON><PERSON>eader from './home/<USER>/CardHeader'
import CardBody from './home/<USER>/CardBody'
import CardFooter from './home/<USER>/CardFooter'

import Comments from './home/<USER>'
import InputComment from './home/<USER>'

const PostCard = ({post, theme}) => {
    return (
        <div className="card my-3"> 
            <CardHeader post={post} />
            <CardBody post={post} theme={theme} />
            <CardFooter post={post} />

            <Comments post={post} />
            <InputComment post={post} />
        </div>
    )
}

export default PostCard
