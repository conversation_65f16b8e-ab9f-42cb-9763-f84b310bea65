import React from 'react'
import { Route, Redirect } from 'react-router-dom'
import { useSelector } from 'react-redux'

const AdminRouter = (props) => {
    const { auth } = useSelector(state => state)
    
    // Check if user is logged in and has admin role
    return (
        auth.token && auth.user.role === 'admin' 
        ? <Route {...props} /> 
        : <Redirect to="/" />
    )
}

export default AdminRouter
