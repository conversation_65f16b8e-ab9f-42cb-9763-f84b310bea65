[{"D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\index.js": "1", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\reportWebVitals.js": "2", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\App.js": "3", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\store.js": "4", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\SocketClient.js": "5", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\login.js": "6", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\customRouter\\PageRender.js": "7", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\postAction.js": "8", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\register.js": "9", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\customRouter\\PrivateRouter.js": "10", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\home.js": "11", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\authAction.js": "12", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\notifyAction.js": "13", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\alert\\Alert.js": "14", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\globalTypes.js": "15", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\CallModal.js": "16", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\StatusModal.js": "17", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\suggestionsAction.js": "18", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\header\\Header.js": "19", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\index.js": "20", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\message\\[id].js": "21", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\discover.js": "22", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\message\\index.js": "23", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\post\\[id].js": "24", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\profile\\[id].js": "25", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\messageAction.js": "26", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\NotFound.js": "27", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\alert\\Loading.js": "28", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\alert\\Toast.js": "29", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\Icons.js": "30", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\Avatar.js": "31", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\header\\Menu.js": "32", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\header\\Search.js": "33", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\mediaShow.js": "34", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\valid.js": "35", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\fetchData.js": "36", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\imageUpload.js": "37", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\Status.js": "38", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\Posts.js": "39", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\RightSideBar.js": "40", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\alertReducer.js": "41", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\authReducer.js": "42", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\profileReducer.js": "43", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\themeReducer.js": "44", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\postReducer.js": "45", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\statusReducer.js": "46", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\notifyReducer.js": "47", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\suggestionsReducer.js": "48", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\callReducer.js": "49", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\onlineReducer.js": "50", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\peerReducer.js": "51", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\messageReducer.js": "52", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\modalReducer.js": "53", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\socketReducer.js": "54", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\detailPostReducer.js": "55", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\discoverReducer.js": "56", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\LeftSide.js": "57", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\RightSide.js": "58", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\discoverAction.js": "59", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\LoadMoreBtn.js": "60", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\PostThumb.js": "61", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\PostCard.js": "62", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\profileAction.js": "63", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Posts.js": "64", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Saved.js": "65", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Info.js": "66", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\NotifyModal.js": "67", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\UserCard.js": "68", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\FollowBtn.js": "69", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\MsgDisplay.js": "70", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\Comments.js": "71", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\InputComment.js": "72", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\post_card\\CardHeader.js": "73", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\post_card\\CardBody.js": "74", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\post_card\\CardFooter.js": "75", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\EditProfile.js": "76", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Followers.js": "77", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Following.js": "78", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\Times.js": "79", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\commentAction.js": "80", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\Carousel.js": "81", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\LikeButton.js": "82", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\ShareModal.js": "83", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\config.js": "84", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\comments\\CommentDisplay.js": "85", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\comments\\CommentCard.js": "86", "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\comments\\CommentMenu.js": "87"}, {"size": 610, "mtime": 1737444774382, "results": "88", "hashOfConfig": "89"}, {"size": 375, "mtime": 1737444774399, "results": "90", "hashOfConfig": "89"}, {"size": 2827, "mtime": 1737445063014, "results": "91", "hashOfConfig": "89"}, {"size": 512, "mtime": 1737444774399, "results": "92", "hashOfConfig": "89"}, {"size": 5587, "mtime": 1737444774347, "results": "93", "hashOfConfig": "89"}, {"size": 2773, "mtime": 1737444774383, "results": "94", "hashOfConfig": "89"}, {"size": 750, "mtime": 1737444774379, "results": "95", "hashOfConfig": "89"}, {"size": 6751, "mtime": 1737444774390, "results": "96", "hashOfConfig": "89"}, {"size": 5993, "mtime": 1737444774386, "results": "97", "hashOfConfig": "89"}, {"size": 251, "mtime": 1737444774380, "results": "98", "hashOfConfig": "89"}, {"size": 1391, "mtime": 1737444774383, "results": "99", "hashOfConfig": "89"}, {"size": 3033, "mtime": 1737444774387, "results": "100", "hashOfConfig": "89"}, {"size": 2238, "mtime": 1737444774390, "results": "101", "hashOfConfig": "89"}, {"size": 970, "mtime": 1737444774364, "results": "102", "hashOfConfig": "89"}, {"size": 531, "mtime": 1737444774389, "results": "103", "hashOfConfig": "89"}, {"size": 9486, "mtime": 1737444774373, "results": "104", "hashOfConfig": "89"}, {"size": 7625, "mtime": 1737444774362, "results": "105", "hashOfConfig": "89"}, {"size": 690, "mtime": 1737444774391, "results": "106", "hashOfConfig": "89"}, {"size": 742, "mtime": 1737444774365, "results": "107", "hashOfConfig": "89"}, {"size": 912, "mtime": 1737444774394, "results": "108", "hashOfConfig": "89"}, {"size": 506, "mtime": 1737444774384, "results": "109", "hashOfConfig": "89"}, {"size": 1660, "mtime": 1737444774383, "results": "110", "hashOfConfig": "89"}, {"size": 720, "mtime": 1737444774384, "results": "111", "hashOfConfig": "89"}, {"size": 1179, "mtime": 1737444774385, "results": "112", "hashOfConfig": "89"}, {"size": 1839, "mtime": 1737444774386, "results": "113", "hashOfConfig": "89"}, {"size": 3394, "mtime": 1737444774389, "results": "114", "hashOfConfig": "89"}, {"size": 408, "mtime": 1737444774360, "results": "115", "hashOfConfig": "89"}, {"size": 554, "mtime": 1737444774364, "results": "116", "hashOfConfig": "89"}, {"size": 772, "mtime": 1737444774365, "results": "117", "hashOfConfig": "89"}, {"size": 1270, "mtime": 1737444774359, "results": "118", "hashOfConfig": "89"}, {"size": 332, "mtime": 1737444774358, "results": "119", "hashOfConfig": "89"}, {"size": 3432, "mtime": 1737444774366, "results": "120", "hashOfConfig": "89"}, {"size": 2368, "mtime": 1737444774366, "results": "121", "hashOfConfig": "89"}, {"size": 399, "mtime": 1737444774406, "results": "122", "hashOfConfig": "89"}, {"size": 1324, "mtime": 1737444774408, "results": "123", "hashOfConfig": "89"}, {"size": 938, "mtime": 1737444774406, "results": "124", "hashOfConfig": "89"}, {"size": 1054, "mtime": 1737444774406, "results": "125", "hashOfConfig": "89"}, {"size": 704, "mtime": 1737444774368, "results": "126", "hashOfConfig": "89"}, {"size": 1385, "mtime": 1737444774367, "results": "127", "hashOfConfig": "89"}, {"size": 2064, "mtime": 1737444774367, "results": "128", "hashOfConfig": "89"}, {"size": 325, "mtime": 1737444774392, "results": "129", "hashOfConfig": "89"}, {"size": 320, "mtime": 1737444774392, "results": "130", "hashOfConfig": "89"}, {"size": 1558, "mtime": 1737444774397, "results": "131", "hashOfConfig": "89"}, {"size": 326, "mtime": 1737444774398, "results": "132", "hashOfConfig": "89"}, {"size": 1278, "mtime": 1737444774396, "results": "133", "hashOfConfig": "89"}, {"size": 294, "mtime": 1737444774397, "results": "134", "hashOfConfig": "89"}, {"size": 1418, "mtime": 1737444774395, "results": "135", "hashOfConfig": "89"}, {"size": 603, "mtime": 1737444774398, "results": "136", "hashOfConfig": "89"}, {"size": 287, "mtime": 1737444774393, "results": "137", "hashOfConfig": "89"}, {"size": 404, "mtime": 1737444774396, "results": "138", "hashOfConfig": "89"}, {"size": 287, "mtime": 1737444774396, "results": "139", "hashOfConfig": "89"}, {"size": 3098, "mtime": 1737444774394, "results": "140", "hashOfConfig": "89"}, {"size": 326, "mtime": 1737444774395, "results": "141", "hashOfConfig": "89"}, {"size": 291, "mtime": 1737444774397, "results": "142", "hashOfConfig": "89"}, {"size": 468, "mtime": 1737444774393, "results": "143", "hashOfConfig": "89"}, {"size": 972, "mtime": 1737444774393, "results": "144", "hashOfConfig": "89"}, {"size": 4795, "mtime": 1737444774373, "results": "145", "hashOfConfig": "89"}, {"size": 9576, "mtime": 1737444774374, "results": "146", "hashOfConfig": "89"}, {"size": 712, "mtime": 1737444774388, "results": "147", "hashOfConfig": "89"}, {"size": 444, "mtime": 1737444774360, "results": "148", "hashOfConfig": "89"}, {"size": 1451, "mtime": 1737444774362, "results": "149", "hashOfConfig": "89"}, {"size": 635, "mtime": 1737444774361, "results": "150", "hashOfConfig": "89"}, {"size": 5370, "mtime": 1737444774391, "results": "151", "hashOfConfig": "89"}, {"size": 1523, "mtime": 1737444774377, "results": "152", "hashOfConfig": "89"}, {"size": 1660, "mtime": 1737444774379, "results": "153", "hashOfConfig": "89"}, {"size": 4016, "mtime": 1737444774377, "results": "154", "hashOfConfig": "89"}, {"size": 4172, "mtime": 1737444774361, "results": "155", "hashOfConfig": "89"}, {"size": 2365, "mtime": 1737444774363, "results": "156", "hashOfConfig": "89"}, {"size": 1503, "mtime": 1737444774358, "results": "157", "hashOfConfig": "89"}, {"size": 3533, "mtime": 1737444774374, "results": "158", "hashOfConfig": "89"}, {"size": 1653, "mtime": 1737444774366, "results": "159", "hashOfConfig": "89"}, {"size": 1738, "mtime": 1737444774367, "results": "160", "hashOfConfig": "89"}, {"size": 2787, "mtime": 1737444774372, "results": "161", "hashOfConfig": "89"}, {"size": 1180, "mtime": 1737444774371, "results": "162", "hashOfConfig": "89"}, {"size": 3532, "mtime": 1737444774371, "results": "163", "hashOfConfig": "89"}, {"size": 4726, "mtime": 1737444774375, "results": "164", "hashOfConfig": "89"}, {"size": 1186, "mtime": 1737444774376, "results": "165", "hashOfConfig": "89"}, {"size": 1152, "mtime": 1737444774377, "results": "166", "hashOfConfig": "89"}, {"size": 897, "mtime": 1737444774375, "results": "167", "hashOfConfig": "89"}, {"size": 4220, "mtime": 1737444774388, "results": "168", "hashOfConfig": "89"}, {"size": 2432, "mtime": 1737444774358, "results": "169", "hashOfConfig": "89"}, {"size": 544, "mtime": 1737444774359, "results": "170", "hashOfConfig": "89"}, {"size": 1353, "mtime": 1737444774362, "results": "171", "hashOfConfig": "89"}, {"size": 63, "mtime": 1737444774405, "results": "172", "hashOfConfig": "89"}, {"size": 1644, "mtime": 1737444774370, "results": "173", "hashOfConfig": "89"}, {"size": 6080, "mtime": 1737444774368, "results": "174", "hashOfConfig": "89"}, {"size": 2092, "mtime": 1737444774370, "results": "175", "hashOfConfig": "89"}, {"filePath": "176", "messages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ww9y0t", {"filePath": "178", "messages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\index.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\reportWebVitals.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\App.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\store.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\SocketClient.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\login.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\customRouter\\PageRender.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\postAction.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\register.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\customRouter\\PrivateRouter.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\home.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\authAction.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\notifyAction.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\alert\\Alert.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\globalTypes.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\CallModal.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\StatusModal.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\suggestionsAction.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\header\\Header.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\index.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\message\\[id].js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\discover.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\message\\index.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\post\\[id].js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\pages\\profile\\[id].js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\messageAction.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\NotFound.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\alert\\Loading.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\alert\\Toast.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\Icons.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\Avatar.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\header\\Menu.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\header\\Search.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\mediaShow.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\valid.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\fetchData.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\imageUpload.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\Status.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\Posts.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\RightSideBar.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\alertReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\authReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\profileReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\themeReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\postReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\statusReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\notifyReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\suggestionsReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\callReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\onlineReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\peerReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\messageReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\modalReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\socketReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\detailPostReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\reducers\\discoverReducer.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\LeftSide.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\RightSide.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\discoverAction.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\LoadMoreBtn.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\PostThumb.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\PostCard.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\profileAction.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Posts.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Saved.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Info.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\NotifyModal.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\UserCard.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\FollowBtn.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\MsgDisplay.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\Comments.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\InputComment.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\post_card\\CardHeader.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\post_card\\CardBody.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\post_card\\CardFooter.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\EditProfile.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Followers.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\profile\\Following.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\message\\Times.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\redux\\actions\\commentAction.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\Carousel.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\LikeButton.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\ShareModal.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\utils\\config.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\comments\\CommentDisplay.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\comments\\CommentCard.js", [], "D:\\gautam\\MERN-Stack-Build-a-social-media-app\\client\\src\\components\\home\\comments\\CommentMenu.js", []]