{"name": "v-network", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "server": "nodemon server.js", "client": "cd client && npm run start", "server-install": "npm install", "client-install": "cd client && npm install", "install-all": "concurrently \"npm run server-install\" \"npm run client-install\" ", "dev": "concurrently \"npm run server\" \"npm run client\" ", "heroku-postbuild": "cd client && npm install && npm run build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.0.0", "concurrently": "^6.0.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^8.2.0", "express": "^4.17.1", "jsonwebtoken": "^8.5.1", "mongoose": "^5.11.13", "peer": "^0.6.1", "postcss": "^8.5.1", "postcss-safe-parser": "^7.0.1", "react": "^16.14.0", "react-dom": "^16.14.0", "socket.io": "^3.1.2"}, "devDependencies": {"nodemon": "^2.0.7"}}