{"name": "client", "proxy": "http://localhost:5000", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^11.2.3", "@testing-library/user-event": "^12.6.2", "axios": "^0.21.1", "bundle": "^2.1.0", "match": "^1.2.10", "moment": "^2.29.1", "peerjs": "^1.3.2", "postcss": "^8.5.1", "postcss-safe-parser": "^7.0.1", "react": "^16.14.0", "react-dom": "^16.14.0", "react-redux": "^7.2.2", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-share": "^4.3.1", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.8", "redux-thunk": "^2.3.0", "socket.io-client": "^3.1.2", "web-vitals": "^0.2.4"}, "scripts": {"start": "react-scripts start", "dev": "react-scripts start --port 5001", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "client": "react-scripts start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"postcss": "8.4.19"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6"}}