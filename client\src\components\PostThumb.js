import React from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';

const PostThumb = ({ posts, result }) => {
    const { theme } = useSelector(state => state);

    // Check if there are no posts
    if (result === 0) return <h2 className="text-center text-danger">No Posts</h2>;

    return (
        <div className="post_thumb">
            {posts.map(post => {
                // Check if post has images and the first image has a URL
                if (!post.images || post.images.length === 0 || !post.images[0].url) {
                    return null; // Skip this post if it doesn't have a valid image
                }

                return (
                    <Link key={post._id} to={`/post/${post._id}`}>
                        <div className="post_thumb_display">
                            {post.images[0].url.match(/video/i) ? (
                                <video
                                    controls
                                    src={post.images[0].url}
                                    alt={`Video: ${post.images[0].url}`}
                                    style={{ filter: theme ? 'invert(1)' : 'invert(0)' }}
                                />
                            ) : (
                                <img
                                    src={post.images[0].url}
                                    alt={`Image: ${post.images[0].url}`}
                                    style={{ filter: theme ? 'invert(1)' : 'invert(0)' }}
                                />
                            )}

                            <div className="post_thumb_menu">
                                <i className="far fa-heart">{post.likes.length}</i>
                                <i className="far fa-comment">{post.comments.length}</i>
                            </div>
                        </div>
                    </Link>
                );
            })}
        </div>
    );
};

export default PostThumb;