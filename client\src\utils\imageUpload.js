// export const checkImage = (file) => {
//     let err = ""
//     if(!file) return err = "File does not exist."

//     if(file.size > 1024 * 1024) // 1mb
//     err = "The largest image size is 1mb."

//     if(file.type !== 'image/jpeg' && file.type !== 'image/png' )
//     err = "Image format is incorrect."
    
//     return err;
// }


// export const imageUpload = async (images) => {
//     let imgArr = [];
//     for(const item of images){
//         const formData = new FormData()

//         if(item.camera){
//             formData.append("file", item.camera)
//         }else{
//             formData.append("file", item)
//         }
        
//         formData.append("upload_preset", "efxjficn")
//         formData.append("cloud_name", "devat-channel")

//         const res = await fetch("https://api.cloudinary.com/v1_1/devat-channel/upload", {
//             method: "POST",
//             body: formData
//         })
        
//         const data = await res.json()
//         imgArr.push({public_id: data.public_id, url: data.secure_url})
//     }
//     return imgArr;
// }

export const checkImage = (file) => {
    let err = "";
    if (!file) return "File does not exist.";

    if (file.size > 1024 * 1024) // 1MB limit
        err = "The largest image size is 1MB.";

    if (file.type !== 'image/jpeg' && file.type !== 'image/png')
        err = "Image format is incorrect.";

    return err;
};

export const imageUpload = async (images) => {
    let imgArr = [];

    for (const item of images) {
        const formData = new FormData();

        if (item.camera) {
            formData.append("file", item.camera);
        } else {
            formData.append("file", item);
        }

        formData.append("upload_preset", "gautam"); // Cloudinary Upload Preset
        formData.append("cloud_name", "dc6du9a5u"); // Updated Cloud Name
        formData.append("api_key", "258231567332274"); // API Key

        try {
            const res = await fetch("https://api.cloudinary.com/v1_1/dc6du9a5u/upload", {
                method: "POST",
                body: formData
            });

            if (!res.ok) {
                const errorData = await res.json();
                throw new Error(errorData.error.message);
            }

            const data = await res.json();
            imgArr.push({ public_id: data.public_id, url: data.secure_url });
        } catch (error) {
            console.error("Error uploading image:", error.message);
        }
    }

    return imgArr;
};