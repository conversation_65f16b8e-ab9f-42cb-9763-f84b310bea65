const router = require('express').Router()
const auth = require('../middleware/auth')
const adminAuth = require('../middleware/adminAuth')
const adminCtrl = require('../controllers/adminCtrl')

// Admin routes - all require both auth and adminAuth middleware
router.get('/admin/users', auth, adminAuth, adminCtrl.getAllUsers)
router.get('/admin/user/:id', auth, adminAuth, adminCtrl.getUserStats)
router.delete('/admin/user/:id', auth, adminAuth, adminCtrl.removeUser)

module.exports = router
